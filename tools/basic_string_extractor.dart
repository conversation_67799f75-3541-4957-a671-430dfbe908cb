import 'dart:io';
import 'dart:convert';

/// Basic string extraction tool for RailOps multi-language implementation
class BasicStringExtractor {
  /// Extract strings from a single file
  static Map<String, List<Map<String, dynamic>>> extractFromFile(String filePath) {
    final file = File(filePath);
    if (!file.existsSync()) return {};

    final content = file.readAsStringSync();
    
    // Skip files that already use localization
    if (content.contains('AppLocalizations.of(context)') || 
        content.contains('AppLocalizations.of(')) {
      print('Skipping already localized file: $filePath');
      return {};
    }

    final Map<String, List<Map<String, dynamic>>> extracted = {
      'text_widgets': [],
      'app_bar_titles': [],
      'form_labels': [],
      'button_labels': [],
    };

    final lines = content.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      
      // Extract Text widget strings
      final textPattern = RegExp(r"Text\s*\(\s*['\"]([^'"]+)['\"]\s*[,\)]");
      final textMatch = textPattern.firstMatch(line);
      if (textMatch != null) {
        final text = textMatch.group(1) ?? '';
        if (_isValidString(text)) {
          extracted['text_widgets']!.add({
            'text': text,
            'file': filePath,
            'line_number': i + 1,
            'suggested_key': _generateKey(text, 'text'),
          });
        }
      }

      // Extract AppBar titles
      final titlePattern = RegExp(r"title:\s*Text\s*\(\s*['\"]([^'"]+)['\"]\s*[,\)]");
      final titleMatch = titlePattern.firstMatch(line);
      if (titleMatch != null) {
        final text = titleMatch.group(1) ?? '';
        if (_isValidString(text)) {
          extracted['app_bar_titles']!.add({
            'text': text,
            'file': filePath,
            'line_number': i + 1,
            'suggested_key': _generateKey(text, 'title'),
          });
        }
      }

      // Extract form labels
      final formPattern = RegExp(r"(?:labelText|hintText|helperText):\s*['\"]([^'"]+)['\"]");
      final formMatch = formPattern.firstMatch(line);
      if (formMatch != null) {
        final text = formMatch.group(1) ?? '';
        if (_isValidString(text)) {
          extracted['form_labels']!.add({
            'text': text,
            'file': filePath,
            'line_number': i + 1,
            'suggested_key': _generateKey(text, 'form'),
          });
        }
      }

      // Extract button labels
      final buttonPattern = RegExp(r"child:\s*Text\s*\(\s*['\"]([^'"]+)['\"]\s*[,\)]");
      final buttonMatch = buttonPattern.firstMatch(line);
      if (buttonMatch != null) {
        final text = buttonMatch.group(1) ?? '';
        if (_isValidString(text)) {
          extracted['button_labels']!.add({
            'text': text,
            'file': filePath,
            'line_number': i + 1,
            'suggested_key': _generateKey(text, 'btn'),
          });
        }
      }
    }

    return extracted;
  }

  /// Extract strings from all Dart files in a directory
  static Future<Map<String, dynamic>> extractFromDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (!directory.existsSync()) {
      throw Exception('Directory does not exist: $dirPath');
    }

    final Map<String, Map<String, List<Map<String, dynamic>>>> allResults = {};
    int totalFiles = 0;
    int totalStrings = 0;

    await for (FileSystemEntity entity in directory.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        // Skip generated files and test files
        if (entity.path.contains('.g.dart') || 
            entity.path.contains('.freezed.dart') ||
            entity.path.contains('.part.dart') ||
            entity.path.contains('_test.dart') ||
            entity.path.contains('/test/') ||
            entity.path.contains('/.dart_tool/')) {
          continue;
        }

        print('Scanning: ${entity.path}');
        final extracted = extractFromFile(entity.path);
        
        if (extracted.isNotEmpty) {
          // Only add files that have extractable strings
          bool hasStrings = false;
          for (var category in extracted.values) {
            if (category.isNotEmpty) {
              hasStrings = true;
              totalStrings += category.length;
            }
          }
          if (hasStrings) {
            allResults[entity.path] = extracted;
            totalFiles++;
          }
        }
      }
    }

    return {
      'summary': {
        'total_files_scanned': totalFiles,
        'total_strings_found': totalStrings,
        'extraction_timestamp': DateTime.now().toIso8601String(),
      },
      'detailed_results': allResults,
    };
  }

  /// Generate a meaningful key from extracted text
  static String _generateKey(String text, String prefix) {
    // Clean the text for key generation
    String cleanText = text
        .replaceAll(RegExp(r'[^a-zA-Z0-9\s]'), '')
        .trim()
        .toLowerCase();

    // Split into words and take first few words
    List<String> words = cleanText.split(RegExp(r'\s+'));
    words = words.where((word) => word.isNotEmpty).take(3).toList();

    String baseKey = words.join('_');
    return '${prefix}_$baseKey';
  }

  /// Check if string is valid for localization
  static bool _isValidString(String text) {
    // Skip empty strings, very short strings, or obvious non-localizable content
    if (text.trim().isEmpty || text.length < 3) return false;
    
    // Skip URLs, numbers, short codes, etc.
    if (RegExp(r'^https?://').hasMatch(text) ||
        RegExp(r'^\d+$').hasMatch(text) ||
        RegExp(r'^[A-Z]{2,4}$').hasMatch(text) ||
        text.contains('@') ||
        text.contains('://') ||
        text.contains('http') ||
        text.contains('www.') ||
        text.startsWith('assets/') ||
        text.startsWith('images/') ||
        text.contains('.png') ||
        text.contains('.jpg') ||
        text.contains('.svg')) {
      return false;
    }
    
    return true;
  }

  /// Generate ARB entries from extracted strings
  static Map<String, dynamic> generateARBEntries(Map<String, dynamic> extractionResults) {
    Map<String, dynamic> arbEntries = {
      '@@locale': 'en',
    };

    Set<String> usedKeys = <String>{};
    Set<String> uniqueStrings = <String>{};

    // Collect unique strings from all categories
    final detailedResults = extractionResults['detailed_results'] as Map<String, dynamic>;
    
    for (String filePath in detailedResults.keys) {
      final fileResults = detailedResults[filePath] as Map<String, dynamic>;
      
      for (String category in fileResults.keys) {
        final categoryResults = fileResults[category] as List<dynamic>;
        
        for (var item in categoryResults) {
          final text = item['text'] as String;
          
          if (!uniqueStrings.contains(text)) {
            uniqueStrings.add(text);
            
            String key = _generateUniqueKey(item['suggested_key'] as String, usedKeys);
            usedKeys.add(key);
            
            arbEntries[key] = text;
            arbEntries['@$key'] = {
              'description': 'Text from $category: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}',
              'context': category,
            };
          }
        }
      }
    }

    return arbEntries;
  }

  /// Generate unique key to avoid conflicts
  static String _generateUniqueKey(String baseKey, Set<String> usedKeys) {
    String key = baseKey;
    int counter = 1;
    
    while (usedKeys.contains(key)) {
      key = '${baseKey}_$counter';
      counter++;
    }
    
    return key;
  }

  /// Save results to JSON file
  static Future<void> saveResults(Map<String, dynamic> results, String outputPath) async {
    final file = File(outputPath);
    await file.writeAsString(
      const JsonEncoder.withIndent('  ').convert(results),
    );
    print('Results saved to: $outputPath');
  }
}

/// Main execution function
Future<void> main(List<String> args) async {
  print('🚀 RailOps Basic String Extraction Tool');
  print('========================================');
  
  String inputDir = args.isNotEmpty ? args[0] : 'lib';
  String outputFile = args.length > 1 ? args[1] : 'extracted_strings.json';
  String arbOutputFile = args.length > 2 ? args[2] : 'lib/l10n/app_en_extracted.arb';

  print('📁 Scanning directory: $inputDir');
  print('📄 Output file: $outputFile');
  print('🌐 ARB output file: $arbOutputFile');
  print('');

  try {
    // Extract strings from all Dart files
    final results = await BasicStringExtractor.extractFromDirectory(inputDir);
    
    if (results['detailed_results'].isEmpty) {
      print('❌ No strings found or all files are already localized.');
      return;
    }

    // Save detailed results
    await BasicStringExtractor.saveResults(results, outputFile);
    
    // Generate and save ARB entries
    final arbEntries = BasicStringExtractor.generateARBEntries(results);
    await BasicStringExtractor.saveResults(arbEntries, arbOutputFile);
    
    // Print summary
    print('✅ Extraction completed successfully!');
    print('');
    print('📊 Summary:');
    print('   Files scanned: ${results['summary']['total_files_scanned']}');
    print('   Total strings found: ${results['summary']['total_strings_found']}');
    print('');
    print('📁 Files generated:');
    print('   📄 Detailed results: $outputFile');
    print('   🌐 ARB file: $arbOutputFile');
    print('');
    print('🔄 Next steps:');
    print('   1. Review the generated ARB file');
    print('   2. Update lib/l10n/app_en.arb with relevant strings');
    print('   3. Run code replacement tool to replace hardcoded strings');
    
  } catch (e) {
    print('❌ Error during extraction: $e');
    exit(1);
  }
}
